<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { page } from '$app/stores';

	import {
		Table,
		TableHead,
		TableHeadCell,
		TableBody,
		TableBodyRow,
		TableBodyCell,
		Input,
		Tooltip,
		Indicator,
    Breadcrumb, 
    BreadcrumbItem,
		Button,
		Dropdown,
		Checkbox
	} from 'flowbite-svelte';
	import { PhoneSolid } from 'flowbite-svelte-icons';
	import {
		UsersSolid,
		CaretDownSolid,
		CaretUpSolid,
		EditSolid,
		SearchOutline,
		ChevronDownOutline,
		AdjustmentsHorizontalSolid
	} from 'flowbite-svelte-icons';
	import type { PageData } from './$types';
	import Pagination from '$src/lib/components/UI/pagination.svelte';
    import { formatTimestamp, displayDate } from '$lib/utils';
	import { getColorClass } from '$lib/utils';
	import { CustomerService } from '$src/lib/api/features/customer/customers.service';
	import { onMount } from 'svelte';
	import type { CustomerInterface } from '$lib/api/types/customer';

	export let data: PageData;
	$: ({ customers: initialCustomers, error } = data);

	console.log(initialCustomers)

	// Always use server-side pagination
	let customers: CustomerInterface[] = [];
	let isLoading = false;

    $: role = $page.data.role;
    $: isAgent = role === 'Agent';

	// Filter state - make these directly reactive instead of nested in an object
	let selectedTags = new Set(['All']);

	// Filter options (loaded from API)
	let tagOptions: Array<{ id: number; name: string; color: string }> = [];

	// Available options with proper typing
	$: tagOptionsList = [
        { name: 'All', id: 'All' },
        ...tagOptions.sort((a: any, b: any) => a.name.localeCompare(b.name))
    ];

	function maskPhoneNumber(phone: string): string {
        if (!phone) return '';
        if (isAgent) {
            const len = phone.length;
            if (len <= 4) return phone;
            return phone.slice(0, 3) + 'x'.repeat(len - 5) + phone.slice(len - 2);
        }
        return phone;
    }

	let searchQuery = '';
	let sortColumn: keyof (typeof customers)[0] = 'customer_id';
	let sortDirection: 'asc' | 'desc' = 'asc';

	// Server-side ordering mapping
	const columnFieldMap = {
        'customer_id': 'customer_id',
        'name': 'name',
        'email': 'email',
        'platforms': 'first_platform_name' // Backend annotated field for platform sorting
    };

	let searchId = '';
	let searchPhone = '';
	let searchEmail = '';

	function sortBy(column: keyof (typeof customers)[0]) {
		handleServerSort(column);
	}

	// Server-side sorting
	function handleServerSort(column: string) {
        const backendField = columnFieldMap[column];
        if (!backendField) return;
        
        let ordering = backendField;
        if (sortColumn === column && sortDirection === 'asc') {
            ordering = '-' + backendField;
            sortDirection = 'desc';
        } else {
            sortDirection = 'asc';
        }
        sortColumn = column;
        
        currentPage = 1;
        fetchCustomers(ordering);
    }

	// Server-side fetch
	const customerService = new CustomerService();
    
    async function fetchCustomers(ordering = 'customer_id') {
        isLoading = true;
        
        // Build filters with better debugging
        const tagFilters = Array.from(selectedTags).filter(t => t !== 'All');
        
        const filters = {
            search: searchQuery.trim() || '',
            tags: tagFilters.join(','),
            page: currentPage,
            limit: itemsPerPage
        };

        console.log('=== FETCH CUSTOMERS DEBUG ===');
        console.log('Selected filter values:');
        console.log('- Selected Tags:', Array.from(selectedTags));
        console.log('- Search Query:', searchQuery);
        console.log('Filters sent to API:', filters);
        console.log('Current ordering:', ordering);
        console.log('Current page:', currentPage);

        try {
            // Use token from server-side data
            const token = data.token || '';

            const response = await customerService.getCustomersWithFiltersAndOrdering(token, filters, ordering);
            
            if (response.res_status === 200) {
                // Handle paginated response structure
                if (response.customers?.results) {
                    customers = response.customers.results;
                    totalItems = response.customers.count || 0;
                    totalPages = Math.ceil(totalItems / itemsPerPage);
                } else {
                    // Fallback for direct array response
                    customers = Array.isArray(response.customers) ? response.customers : [];
                    totalItems = customers.length;
                    totalPages = 1;
                }
            } else {
                console.error('Failed to fetch customers:', response.error_msg);
                customers = [];
            }
        } catch (error) {
            console.error('Error fetching customers:', error);
            customers = [];
        }
        
        isLoading = false;
    }

    // Debounced search
    let searchTimeout: ReturnType<typeof setTimeout>;
    function delayedSearch() {
        if (searchTimeout) clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            currentPage = 1;
            fetchCustomers();
        }, 500);
    }
    
    $: searchQuery, delayedSearch();

	function filterAll(item: any, term: string) {
		const t = term.toLowerCase();
		return [
			String(item.customer_id), // ID
			item.name || (item.first_name && item.last_name ? `${item.first_name} ${item.last_name}` : '') || item.line_user?.display_name, // Name
			item.phone, // Phone
			item.platforms?.name, // Platform
			item.email, // Email
			displayDate(item.created_on) // Created On
		]
			.filter(Boolean)
			.some((field) => field.toLowerCase().includes(t));
	}

	function compare(a: any, b: any) {
		let av = a[sortColumn],
			bv = b[sortColumn];
		if (sortColumn === 'created_on') {
			av = new Date(av).getTime();
			bv = new Date(bv).getTime();
		}
		if (typeof av === 'number' && typeof bv === 'number') {
			return sortDirection === 'asc' ? av - bv : bv - av;
		}
		return sortDirection === 'asc'
			? String(av).localeCompare(String(bv))
			: String(bv).localeCompare(String(av));
	}

	// Calculate statistics
	$: totalCustomers = totalItems || customers?.length || 0;

	//////////////// Pagination Logic ////////////////
	// pagination state variables
	let currentPage = 1;
	let itemsPerPage = 10;
	let totalItems = 0;
	let totalPages = 1;

	// Update pagination calculation
	$: totalPages = Math.ceil(totalItems / itemsPerPage) || 1;
	$: paginatedCustomers = customers;

	function updateCurrentPage(newCurrentPage: number) {
		currentPage = newCurrentPage;
		fetchCustomers();
	}
	
	// Filter toggle functions
	function toggleTag(tag: { name: string; id: string | number }) {
		const newSet = new Set(selectedTags);
		const tagName = tag.name;

		if (tagName === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(tagName)) {
				newSet.delete(tagName);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(tagName);
			}
		}

		selectedTags = newSet;
		currentPage = 1;
		fetchCustomers();
	}

	function resetFilters() {
		selectedTags = new Set(['All']);
		searchQuery = '';
		currentPage = 1;
		fetchCustomers();
	}

	// Load filter options
	async function loadFilterOptions() {
		try {
			const token = data.token || '';

			// Load tags
			const tagsResponse = await customerService.getFilterTags(token);
			if (tagsResponse.res_status === 200) {
				tagOptions = tagsResponse.data;
			}
		} catch (error) {
			console.error('Error loading filter options:', error);
		}
	}

	// Format tag name to capitalize first letter
    // TODO - I remember I use this filter explicitly once in the code, but I cannot find it
    // Use this function instead of inline formatting if found
	function formatTagName(tag: string): string {
		// return tag.charAt(0).toUpperCase() + tag.slice(1);
		return tag.charAt(0).toUpperCase() + tag.slice(1).split('_').join(' ');
	}

	// Initialize
	onMount(() => {
		loadFilterOptions();
		fetchCustomers();
	});
</script>

<svelte:head>
	<title>Customers</title>
</svelte:head>

<div class="flex h-screen">
	<div class="w-full overflow-y-auto bg-white p-8">

        <Breadcrumb aria-label="Default breadcrumb example" class="mb-3">
            <BreadcrumbItem href="/" home>
                <span class="text-gray-400">{t('home')}</span>
            </BreadcrumbItem>
            <BreadcrumbItem>
                <span class="text-gray-700">{t('customers')}</span>
            </BreadcrumbItem>
        </Breadcrumb>

		<div class="mb-6">
			<h2 class="text-2xl font-bold">{t('customers')}</h2>
			<p class="text-gray-600">{totalCustomers} {t('customers')}</p>
		</div>

		<!-- Filters and Search Bar - Improved Layout -->
		<div
			class="mb-6 flex flex-col items-start gap-4 lg:flex-row lg:items-center lg:justify-between"
		>
			<!-- Left side - Filter Buttons -->
			<div class="flex flex-wrap gap-3">
				<!-- Tag Filter -->
				<div>
					<Button
                        color={!selectedTags.has('All') ? 'dark' : 'none'}
                        class={`${!selectedTags.has('All') ? '' : 'hover:bg-gray-100 border'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_tag')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown class="w-72 p-2 shadow-lg max-h-80 overflow-y-auto">
                        {#each tagOptionsList as tag}
                            <div class="rounded p-2 hover:bg-gray-100 flex justify-between items-center">
                                <Checkbox
                                    checked={selectedTags.has(tag.name)}
                                    on:change={() => toggleTag(tag)}
                                    class="flex items-center"
                                >
                                    <span class="ml-2 text-sm">
                                        {tag.name === 'All' ? tag.name : formatTagName(tag.name)}
                                    </span>
                                </Checkbox>
                            </div>
                        {/each}
                    </Dropdown>
				</div>

				<!-- Reset Filter -->
				<Button 
                    color="none" 
                    on:click={resetFilters}
                    class="hover:bg-gray-100 border w-auto shadow-md"
                >
                    {t('filter_reset')}
                </Button>

				<!-- Loading indicator -->
				{#if isLoading}
					<div class="flex items-center gap-2">
						<span class="text-sm text-gray-500">Loading...</span>
					</div>
				{/if}
			</div>

			<!-- Right side - Search Bar -->
			<div class="relative w-full lg:w-1/3 shadow-md">
				<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
					<SearchOutline class="h-5 w-5 text-gray-500" />
				</div>
				<Input
					id="searchBar"
					type="text"
					placeholder="Search by no., name, tag, phone, platform, email..."
					bind:value={searchQuery}
					class={`bg-white block w-full pl-10 py-2.5 border rounded-lg
                        focus:outline-none focus:ring-2 focus:ring-blue-700 focus:border-blue-700
                        ${searchQuery ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'}`}
				/>
			</div>
		</div>

		{#if error}
			<p class="text-red-600">{error}</p>
		{:else}
			<Table sort={compare} hoverable shadow class="table-fixed w-full">
				<TableHead>
					<TableHeadCell class="w-[70px]" on:click={() => sortBy('customer_id')}>
						<div class="flex items-center">
							{t('table_no')}
							{#if sortColumn === 'customer_id'}
								{#if sortDirection === 'asc'}
									<CaretUpSolid class="ml-1 h-4 w-4" />
								{:else}
									<CaretDownSolid class="ml-1 h-4 w-4" />
								{/if}
							{/if}
						</div>
					</TableHeadCell>

					<TableHeadCell class="w-[250px]"  on:click={() => sortBy('name')}>
						<div class="flex items-center">
							{t('table_name')}
							{#if sortColumn === 'name'}
								{#if sortDirection === 'asc'}
									<CaretUpSolid class="ml-1 h-4 w-4" />
								{:else}
									<CaretDownSolid class="ml-1 h-4 w-4" />
								{/if}
							{/if}
						</div>
					</TableHeadCell>

                    <TableHeadCell class="w-[150px]">
						{t('table_tag')}
					</TableHeadCell>

					<TableHeadCell class="w-[180px]">{t('table_phone')}</TableHeadCell>

					<TableHeadCell class="w-[150px]" on:click={() => sortBy('platforms')}>
						<div class="flex items-center">
							{t('table_platform')}
							{#if sortColumn === 'platforms'}
								{#if sortDirection === 'asc'}
									<CaretUpSolid class="ml-1 h-4 w-4" />
								{:else}
									<CaretDownSolid class="ml-1 h-4 w-4" />
								{/if}
							{/if}
						</div>
					</TableHeadCell>

					<TableHeadCell class="w-[280px]" on:click={() => sortBy('email')}>
						<div class="flex items-center">
							{t('table_email')}
							{#if sortColumn === 'email'}
								{#if sortDirection === 'asc'}
									<CaretUpSolid class="ml-1 h-4 w-4" />
								{:else}
									<CaretDownSolid class="ml-1 h-4 w-4" />
								{/if}
							{/if}
						</div>
					</TableHeadCell>

					<!-- <TableHeadCell class="w-[150px]" on:click={() => sortBy('created_on')}>{t('table_created_on')}</TableHeadCell> -->
				</TableHead>

				<TableBody>
                    {#if paginatedCustomers.length === 0}
                        <TableBodyRow>
                            <TableBodyCell colspan={9} class="text-center py-4 text-gray-500">
                                {t('table_no_data')}
                            </TableBodyCell>
                        </TableBodyRow>
                    {:else}
                        {#each paginatedCustomers as item}
                            <TableBodyRow slot="row">
                                <TableBodyCell>
                                    <a
                                        href={`/customer/${item.customer_id}`}
                                        class="flex items-center text-blue-600 hover:underline py-2"
                                    >
                                        {item.customer_id}
                                        <EditSolid class="ml-1 h-4 w-4" />
                                    </a>
                                </TableBodyCell>

                                <TableBodyCell>
                                    <span class="break-words">
                                        <!-- {item.name ? item.name : item.line_user ? item.name : '-'} -->
										{item.first_name && item.last_name ? `${item.first_name} ${item.last_name}` : item.name || '-'}
                                    </span>
                                </TableBodyCell>

                                <TableBodyCell>
                                    {#if item.tag}
                                        <!-- Show single tag badge -->
                                        <span
                                            class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"
                                        >
                                            <Indicator size="sm" class={`mr-1 ${getColorClass(item.tag.color)} inline-block`} />
                                            {formatTagName(item.tag.name)}
                                        </span>
                                    {:else}
                                        -
                                    {/if}
                                </TableBodyCell>

                                <TableBodyCell>{item.phone ? maskPhoneNumber(item.phone) : '-'}</TableBodyCell>
                                <TableBodyCell>
                                    {#if item.platforms?.name === 'LINE'}
                                        <div class="flex items-center">
                                            <img src="/images/line-icon.png" alt="LINE Icon" class="mr-2 h-5 w-5" />
                                            {item.platforms.name}
                                        </div>
                                    {:else if item.platforms?.name === 'FACEBOOK'}
                                        <div class="flex items-center">
                                            <img src="/images/facebook-icon.png" alt="Facebook Icon" class="mr-2 h-5 w-5" />
                                            {item.platforms.name}
                                        </div>
                                    {:else if item.platforms?.name === 'TELEPHONE'}
                                        <div class="flex items-center">
                                            <PhoneSolid class="mr-2 h-5 w-5" />
                                            {item.platforms.name}
                                        </div>
                                    {:else}
                                        {item.platforms?.name || '-'}
                                    {/if}
                                </TableBodyCell>

                                <TableBodyCell>
                                    <span class="break-words">
                                        {item.email ?? '-'}
                                    </span>
                                </TableBodyCell>
                                <!-- <TableBodyCell>
                                    <div>{displayDate(item.created_on).date}</div>
                                    <div>{displayDate(item.created_on).time}</div>
                                </TableBodyCell> -->
                            </TableBodyRow>
                        {/each}
                    {/if}
				</TableBody>
			</Table>

            <!-- Pagination Layout -->
			<Pagination {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />
		{/if}
	</div>
</div>

<style>
	.break-words {
		word-break: break-word;
		white-space: normal;
	}
</style>