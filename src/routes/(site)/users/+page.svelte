<script lang="ts">
    import { t } from '$lib/stores/i18n';
	import { enhance } from '$app/forms';
    
	import {
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
		Table,
		Button,
		Dropdown,
		Checkbox,
		Spinner,
		Indicator,
		Avatar,
		Badge,
		Input,
		Tooltip
	} from 'flowbite-svelte';
	import {
		EditSolid,
		ChevronDownOutline,
		UserHeadsetSolid,
		AdjustmentsHorizontalSolid,
		UserCircleSolid,
		SearchOutline
	} from 'flowbite-svelte-icons';
    import { getColorClass } from '$lib/utils';

	import { CaretDownSolid, CaretUpSolid } from 'flowbite-svelte-icons';
	import { formatTimestamp, displayDate, timeAgo } from '$lib/utils';
	import type { PageData } from './$types';
	import UserSignUp from '$lib/components/UI/UserSignUp.svelte';
    import Pagination from '$src/lib/components/UI/pagination.svelte';
    import AdminStatistics from '$src/lib/components/admin/AdminStatistics.svelte';
    import { UserService } from '$lib/api/features/user/users.service';
    import { onMount } from 'svelte';
	export let data: PageData;

	$: ({ users: initialUsers, statuses, roles, partners, departments, error, all_tags, token } = data);

    // Current role
    // console.log("data.role = " + data.role);

	interface User {
		id: number;
		roles: string;
		partners: string;
		username: string;
		email: string;
		status: string;
		is_active: boolean;
		name: string;
		employee_id: number;
		current_workload: number;
		last_active: string;
	}

	// Filter state - make these directly reactive instead of nested in an object
	let selectedStatuses = new Set(['All']);
	let selectedRoles = new Set(['All']);
	let selectedPartners = new Set(['All']);
	let selectedDepartments = new Set(['All']);
	let selectedSpecializedTags = new Set(['All']);
	let selectedActiveStatus = 'All';

	// Available options with proper typing
	const statusOptions = ['All', 'online', 'busy', 'away', 'offline'];
	
	// Fixed role options instead of extracting from users
	const roleOptions = ['All', 'Admin', 'System', 'Supervisor', 'Agent'];

    $: partnerOptions = [
        { name: 'All', code: 'All' },
        ...Array.from(
            new Map(
                (partners || [])
                    .filter((partner: any) => partner.name && partner.name !== '' && partner.name.toLowerCase() !== 'all')
                    .map((partner: any) => [`${partner.name}-${partner.code}`, { name: partner.name, code: partner.code }])
            ).values()
        ).sort((a: any, b: any) => a.name.localeCompare(b.name))
    ];

    $: departmentOptions = [
        { name: 'All', code: 'All' },
        ...Array.from(
            new Map(
                (departments || [])
                    .filter((dept: any) => dept.name && dept.name !== '')
                    .map((dept: any) => [`${dept.name}-${dept.code}`, { name: dept.name, code: dept.code }])
            ).values()
        ).sort((a: any, b: any) => a.name.localeCompare(b.name))
    ];

    $: specializedTagOptions = [
        'All',
        ...Array.from(
            new Set(
                (all_tags || [])
                    .map((tag: any) => tag.name)
                    .filter((name: string) => name && name !== '')
            )
        ).sort((a: string, b: string) => a.localeCompare(b))
    ];

    $: partnerCounts = {};
    $: departmentCounts = {};
    $: specializedTagCounts = {};
    
    $: {
        partnerCounts = {};
        departmentCounts = {};
        specializedTagCounts = {};
        
        for (const user of users) {
            // Count partners
            for (const partner of user.partners || []) {
                const name = partner.name;
                if (name !== '' && name !== 'all') {
                    partnerCounts[name] = (partnerCounts[name] || 0) + 1;
                }
            }
            
            // Count departments
            for (const dept of user.departments || []) {
                const name = dept.name;
                if (name !== '' && name !== 'all') {
                    departmentCounts[name] = (departmentCounts[name] || 0) + 1;
                }
            }
            
            // Count specialized tags - user.user_tags now contains full tag objects
            for (const tag of user.user_tags || []) {
                const name = tag.name;
                if (name !== '' && name !== 'all') {
                    specializedTagCounts[name] = (specializedTagCounts[name] || 0) + 1;
                }
            }
        }
    };

	const activeStatusOptions = ['All', 'Active', 'Inactive'];

	// Helper function to check if arrays have common elements
	function hasCommonElement(selected: Set<string>, userItems: string): boolean {
		if (selected.has('All')) return true;
		if (!userItems) return false;

		const userItemsArray = userItems.split(',').map((item) => item.trim());
		return Array.from(selected).some(
			(selectedItem) => selectedItem !== 'All' && userItemsArray.includes(selectedItem)
		);
	}    
    
    function isPartnersContained(
        partners: { name: string; code: string }[],
        searchSet: Set<string>
    ): boolean {
        const loweredSet = new Set(Array.from(searchSet).map((s) => s.toLowerCase()));

        return partners.some((partner) =>
            loweredSet.has(partner.name.toLowerCase())
        );
    }

	// Always use server-side pagination
	let users: any[] = [];

	function toggleStatus(status: string) {
		const newSet = new Set(selectedStatuses);

		if (status === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(status)) {
				newSet.delete(status);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(status);
			}
		}

		selectedStatuses = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function toggleRole(role: string) {
		const newSet = new Set(selectedRoles);

		if (role === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(role)) {
				newSet.delete(role);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(role);
			}
		}

		selectedRoles = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function togglePartner(partner: { name: string; code: string }) {
		const newSet = new Set(selectedPartners);
		const partnerName = partner.name;

		if (partnerName === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(partnerName)) {
				newSet.delete(partnerName);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(partnerName);
			}
		}

		selectedPartners = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function toggleDepartment(department: { name: string; code: string }) {
		const newSet = new Set(selectedDepartments);
		const departmentName = department.name;

		if (departmentName === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(departmentName)) {
				newSet.delete(departmentName);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(departmentName);
			}
		}

		selectedDepartments = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function toggleSpecializedTag(tag: string) {
		const newSet = new Set(selectedSpecializedTags);

		if (tag === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(tag)) {
				newSet.delete(tag);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(tag);
			}
		}

		selectedSpecializedTags = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function resetFilters() {
		selectedStatuses = new Set(['All']);
		selectedRoles = new Set(['All']);
		selectedPartners = new Set(['All']);
		selectedDepartments = new Set(['All']);
		selectedSpecializedTags = new Set(['All']);
		selectedActiveStatus = 'All';
		sortColumn = 'id';
		sortDirection = 'asc';
		searchQuery = '';
		currentPage = 1;
		currentOrdering = 'id';
		fetchUsers('id');
	}
	// Server-side ordering mapping
	const columnFieldMap = {
        'id': 'id',                    // No.
        'status': 'status',            // Status  
        'workload': 'current_workload', // Workload
        'name': 'first_name',          // Name
        'last_active': 'last_active'   // Last Active
    };

	// Server-side fetch
	const userService = new UserService();
    
    let isLoading = false;
    
    async function fetchUsers(ordering = 'id') {
        if (isLoading) return; // To avoid multiple requests
        isLoading = true;
        
        // Build filters with better debugging
        const statusFilters = Array.from(selectedStatuses).filter(s => s !== 'All');
        const roleFilters = Array.from(selectedRoles).filter(r => r !== 'All'); 
        const partnerFilters = Array.from(selectedPartners).filter(p => p !== 'All');
        const departmentFilters = Array.from(selectedDepartments).filter(d => d !== 'All');
        const specializedTagFilters = Array.from(selectedSpecializedTags).filter(t => t !== 'All');
        
        const filters = {
            search: searchQuery.trim() || '',
            status: statusFilters.join(','),
            role: roleFilters.join(','),
            partner: partnerFilters.join(','),
            department: departmentFilters.join(','),
            user_tag: specializedTagFilters.join(','),
            page: currentPage,
            page_size: itemsPerPage
        };

        console.log('=== FETCH USERS DEBUG ===');
        console.log('Selected filter values:');
        console.log('- Selected Statuses:', Array.from(selectedStatuses));
        console.log('- Selected Roles:', Array.from(selectedRoles));
        console.log('- Selected Partners:', Array.from(selectedPartners));
        console.log('- Selected Departments:', Array.from(selectedDepartments));
        console.log('- Selected Specialized Tags:', Array.from(selectedSpecializedTags));
        console.log('- Search Query:', searchQuery);
        console.log('Filters sent to API:', filters);
        console.log('Current ordering:', ordering);
        console.log('Current page:', currentPage);
        console.log('=== FILTER CHANGE TRIGGERED ===');

        try {
            const response = await userService.getUsersWithFiltersAndOrdering(token, filters, ordering);
            
            console.log('API Response status:', response.res_status);
            
            if (response.res_status === 200) {
                // Handle paginated response structure
                if (response.users?.results) {
                    users = response.users.results;
                    // Backend uses 10 items per page based on actual response
                    const backendPageSize = 10;
                    const totalCount = response.users.count || 0;
                    
                    totalPages = Math.ceil(totalCount / backendPageSize);
                    totalItems = totalCount;
                    
                    console.log('Results received:');
                    console.log('- Results length:', response.users.results.length);
                    console.log('- Total count:', totalCount);
                    console.log('- Total pages:', totalPages);
                    console.log('- Current page:', currentPage);
                    console.log('- Has next:', !!response.users.next);
                } else {
                    // Fallback for non-paginated response
                    users = response.users || [];
                    totalItems = users.length;
                    totalPages = 1;
                }
            } else {
                console.error('Failed to fetch users:', response.error_msg);
                users = [];
                totalItems = 0;
                totalPages = 1;
            }
        } catch (error) {
            console.error('Failed to fetch users:', error.message);
            users = [];
            totalItems = 0;
            totalPages = 1;
        }
        
        isLoading = false;
    }

    // Debounced search
    let searchTimeout: ReturnType<typeof setTimeout>;
    function delayedSearch() {
        if (searchTimeout) clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            currentPage = 1;
            fetchUsers();
        }, 500);
    }
    
    $: searchQuery, delayedSearch();

	// Declare searchQuery
	let searchQuery = '';

	// Sort state for each column
	let sortColumn = 'id'; // Default to sorting by "ID"
	let sortDirection = 'asc'; // Default to ascending order
	let currentOrdering = 'id'; // Track current ordering for pagination

	// Function to toggle sorting
	function sortBy(column: string) {
		if (sortColumn === column) {
			// Toggle direction if already sorted by this column
			sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
		} else {
			// Sort by the new column, default to ascending
			sortColumn = column;
			sortDirection = 'asc';
		}
        
        // Handle server-side sorting
        handleServerSort(column);
	}

    function handleServerSort(column: string) {
        const field = columnFieldMap[column];
        if (field) {
            const ordering = sortDirection === 'desc' ? `-${field}` : field;
            currentOrdering = ordering; // Store current ordering for pagination
            currentPage = 1; // Reset to first page when sorting changes
            fetchUsers(ordering);
        }
    }

	//////////////// Pagination Logic ////////////////
	// pagination state variables
	let currentPage = 1;
	let itemsPerPage = 10;
	let totalItems = 0;
	let totalPages = 1;

	// Update pagination calculation
	$: totalPages = Math.ceil(totalItems / itemsPerPage) || 1;
	$: paginatedUsers = users;

	function updateCurrentPage(newCurrentPage: number) {
		currentPage = newCurrentPage;
		// Use current ordering when changing pages to maintain sort order
		fetchUsers(currentOrdering);
	}
    
    // Format tag name to capitalize first letter
    // TODO - I remember I use this filter explicitly once in the code, but I cannot find it
    // Use this function instead of inline formatting if found
	function formatTagName(tag: string): string {
		return tag.charAt(0).toUpperCase() + tag.slice(1).split('_').join(' ');
	}
    import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';


    // getTagsByIds function removed - no longer needed since user_tags contains full objects


    $: userMatchedTags = users?.map((user) => ({
        id: user.id,
        tags: user.user_tags || []  // user_tags already contains full tag objects
    }));

    // Debug logging removed after fixing the issue

	// Initialize
	onMount(() => {
		fetchUsers();
	});

</script>

<svelte:head>
	<title>Users</title>
</svelte:head>

<div class="flex h-screen">
	<div class="w-4/4 overflow-y-auto bg-white p-8">

        <Breadcrumb aria-label="Default breadcrumb example" class="mb-3">
            <BreadcrumbItem href="/" home>
              <span class="text-gray-400">{t('home')}</span>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <span class="text-gray-700">{t('users')}</span>
            </BreadcrumbItem>
        </Breadcrumb>

		<div class="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
			<!-- Left Section: Title & Description -->
			<div>
				<h2 class="text-2xl font-bold">{t('users_page_title')}</h2>
                <p class="text-gray-600">{t('users_page_description')}</p>
			</div>
			<!-- Right: Buttons in one row -->
            {#if data.role === 'Admin'}
                <div class="flex flex-nowrap gap-2 overflow-x-auto md:overflow-visible">
                    <!-- New User Button -->
                    <UserSignUp count={users?.length || 0} />
                </div>
            {/if}
		</div>

        <AdminStatistics users={users} />



		<!-- Filters and Search Bar - Improved Layout -->
		<div
			class="mb-6 flex flex-col items-start gap-4 lg:flex-row lg:items-center lg:justify-between"
		>
			<!-- Left side - Buttons -->
			<div class="flex flex-wrap gap-3">
				<!-- Status Filter -->
				<div>
					<Button
                        color={!selectedStatuses.has('All') ? 'dark' : 'none'}
                        class={`${!selectedStatuses.has('All') ? '' : 'hover:bg-gray-100 border'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_status')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown class="w-44 p-2 shadow-lg">
						{#each statusOptions as status}
							<div class="rounded p-2 hover:bg-gray-100">
								<Checkbox
									checked={selectedStatuses.has(status)}
									on:change={() => toggleStatus(status)}
									class="flex items-center"
								>
									<span class="ml-2 text-sm">
										{status === 'All'
											? 'All Status'
											: status.charAt(0).toUpperCase() + status.slice(1)}
									</span>
								</Checkbox>
							</div>
						{/each}
					</Dropdown>
				</div>

				<!-- Role Filter -->
				<div>
					<Button
                        color={!selectedRoles.has('All') ? 'dark' : 'none'}
                        class={`${!selectedRoles.has('All') ? '' : 'hover:bg-gray-100 border'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_role')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown class="w-44 p-2 shadow-lg">
						{#each roleOptions as role}
							<div class="rounded p-2 hover:bg-gray-100">
								<Checkbox
									checked={selectedRoles.has(role)}
									on:change={() => toggleRole(role)}
									class="flex items-center"
								>
									<span class="ml-2 text-sm">{role}</span>
								</Checkbox>
							</div>
						{/each}
					</Dropdown>
				</div>

				<!-- Partner Filter -->
				<div>
					<Button
                    color={!selectedPartners.has('All') ? 'dark' : 'none'}
                    class={`${!selectedPartners.has('All') ? '' : 'hover:bg-gray-100 border'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_partner')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown class="w-72 p-2 shadow-lg max-h-80 overflow-y-auto">
                        {#each partnerOptions as partner}
                            <div class="rounded p-2 hover:bg-gray-100 flex justify-between items-center">
                                <Checkbox
                                    checked={selectedPartners.has(partner.name)}
                                    on:change={() => togglePartner(partner)}
                                    class="flex items-center"
                                >
                                    <span class="ml-2 text-sm">
                                        {#if partner.name === 'All'}
                                            {partner.name}
                                        {:else}
                                            {formatTagName(partner.name)} ({partner.code})
                                            <!-- <span class="ml-1 text-gray-500">
                                                ({partnerCounts[partner.name] || 0})
                                            </span> -->
                                        {/if}
                                    </span>
                                </Checkbox>
                            </div>
                        {/each}
                    </Dropdown>
				</div>

				<!-- Department Filter -->
				<div>
					<Button
                    color={!selectedDepartments.has('All') ? 'dark' : 'none'}
                    class={`${!selectedDepartments.has('All') ? '' : 'hover:bg-gray-100 border'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_department')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown class="w-72 p-2 shadow-lg max-h-80 overflow-y-auto">
                        {#each departmentOptions as department}
                            <div class="rounded p-2 hover:bg-gray-100 flex justify-between items-center">
                                <Checkbox
                                    checked={selectedDepartments.has(department.name)}
                                    on:change={() => toggleDepartment(department)}
                                    class="flex items-center"
                                >
                                    <span class="ml-2 text-sm">
                                        {#if department.name === 'All'}
                                            {department.name}
                                        {:else}
                                            {formatTagName(department.name)} ({department.code})
                                            <!-- <span class="ml-1 text-gray-500">
                                                ({departmentCounts[department.name] || 0})
                                            </span> -->
                                        {/if}
                                    </span>
                                </Checkbox>
                            </div>
                        {/each}
                    </Dropdown>
				</div>

				<!-- Specialized Tag Filter -->
				<div>
					<Button
                    color={!selectedSpecializedTags.has('All') ? 'dark' : 'none'}
                    class={`${!selectedSpecializedTags.has('All') ? '' : 'hover:bg-gray-100 border'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_specialized_tag')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown class="w-72 p-2 shadow-lg max-h-80 overflow-y-auto">
                        {#each specializedTagOptions as tag}
                            <div class="rounded p-2 hover:bg-gray-100 flex justify-between items-center">
                                <Checkbox
                                    checked={selectedSpecializedTags.has(tag)}
                                    on:change={() => toggleSpecializedTag(tag)}
                                    class="flex items-center"
                                >
                                    <span class="ml-2 text-sm">
                                        {tag === 'All' ? tag : formatTagName(tag)}
                                        <!-- {#if tag !== 'All'}
                                            ({specializedTagCounts[tag] || 0})
                                        {/if} -->
                                    </span>
                                </Checkbox>
                            </div>
                        {/each}
                    </Dropdown>
				</div>

				<!-- Reset Filter -->
				<Button 
                    color="none" 
                    on:click={resetFilters}
                    class="hover:bg-gray-100 border w-auto shadow-md"
                >
                    {t('filter_reset')}
                </Button>
			</div>

			<!-- Right side - Search Bar -->
			<div class="relative w-full lg:w-1/3 shadow-md">
				<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
					<SearchOutline class="h-5 w-5 text-gray-500" />
				</div>
				<Input
					id="searchBar"
					type="text"
					placeholder={t('search_user_placeholder')}
					bind:value={searchQuery}
					class={`bg-white block w-full pl-10 py-2.5 border rounded-lg
                        focus:outline-none focus:ring-2 focus:ring-blue-700 focus:border-blue-700
                        ${searchQuery ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'}`}
				/>
			</div>
		</div>

		<!-- Table section -->
        <Table shadow class="table-fixed w-full">
            <TableHead>
                <TableHeadCell class="w-[60px]" on:click={() => sortBy('id')}>
                    <div class="flex items-center justify-start">
                        {t('table_no')}
                        {#if sortColumn === 'id'}
                            {#if sortDirection === 'desc'}
                                <CaretDownSolid class="ml-1 h-4 w-4" />
                            {:else}
                                <CaretUpSolid class="ml-1 h-4 w-4" />
                            {/if}
                        {/if}
                    </div>
                </TableHeadCell>
                <TableHeadCell class="w-[90px]" on:click={() => sortBy('status')}>
                    <div class="flex items-center justify-start">
                        {t('table_status')}
                        {#if sortColumn === 'status'}
                            {#if sortDirection === 'desc'}
                                <CaretDownSolid class="ml-1 h-4 w-4" />
                            {:else}
                                <CaretUpSolid class="ml-1 h-4 w-4" />
                            {/if}
                        {/if}
                    </div>
                </TableHeadCell>
                <TableHeadCell class="w-[110px]" on:click={() => sortBy('workload')}>
                    <div class="flex items-center justify-start">
                        {t('table_workload')}
                        {#if sortColumn === 'workload'}
                            {#if sortDirection === 'desc'}
                                <CaretDownSolid class="ml-1 h-4 w-4" />
                            {:else}
                                <CaretUpSolid class="ml-1 h-4 w-4" />
                            {/if}
                        {/if}
                    </div>
                </TableHeadCell>
                <!-- <TableHeadCell class="w-[150px]">Nickname</TableHeadCell> -->
                <TableHeadCell class="w-[200px]" on:click={() => sortBy('name')}>
                    <div class="flex items-center justify-start">
                        {t('table_name')}
                        {#if sortColumn === 'name'}
                            {#if sortDirection === 'desc'}
                                <CaretDownSolid class="ml-1 h-4 w-4" />
                            {:else}
                                <CaretUpSolid class="ml-1 h-4 w-4" />
                            {/if}
                        {/if}
                    </div>
                </TableHeadCell>
                <TableHeadCell class="w-[100px]">{t('table_role')}</TableHeadCell>
                <TableHeadCell class="w-[110px]">{t('table_partner')}</TableHeadCell>
                <TableHeadCell class="w-[110px]">{t('table_department')}</TableHeadCell>
                <TableHeadCell class="w-[110px]">{t('table_specialize_tag')}</TableHeadCell>
                <!-- <TableHeadCell class="w-[120px]" on:click={() => sortBy('last_active')}>
                    <div class="flex items-center justify-start">
                        {#if sortColumn === 'last_active' && sortDirection === 'desc'}
                            {t('table_time')} <CaretUpSolid class="inline-block h-4 w-4" />
                        {:else}
                            {t('table_time')} <CaretDownSolid class="inline-block h-4 w-4" />
                        {/if}
                    </div>
                </TableHeadCell> -->
                <TableHeadCell class="w-[150px]" on:click={() => sortBy('last_active')}>
                    <div class="flex items-center justify-start">
                        {t('table_last_active')}
                        {#if sortColumn === 'last_active'}
                            {#if sortDirection === 'desc'}
                                <CaretDownSolid class="ml-1 h-4 w-4" />
                            {:else}
                                <CaretUpSolid class="ml-1 h-4 w-4" />
                            {/if}
                        {/if}
                    </div>
                </TableHeadCell>
            </TableHead>
            <TableBody>
                {#if paginatedUsers.length === 0}
                    <TableBodyRow>
                        <TableBodyCell colspan={9} class="text-center py-4 text-gray-500">
                            No user(s) to display
                        </TableBodyCell>
                    </TableBodyRow>
                {:else}
                    {#each paginatedUsers as user}
                        <TableBodyRow>
                            <TableBodyCell>
                                <a
                                    href="/users/{user.id}"
                                    class="flex items-center justify-start text-blue-600 hover:underline py-2"
                                >
                                    {user.id}<EditSolid class="h-4 w-4" />
                                </a>
                            </TableBodyCell>
                            <TableBodyCell>
                                <span
                                    class={user.status === 'online'
                                        ? 'inline-block rounded-md bg-green-200 px-2 py-1 text-sm text-green-700'
                                        : user.status === 'busy'
                                            ? 'inline-block rounded-md bg-red-200 px-2 py-1 text-sm text-red-700'
                                            : user.status === 'away'
                                                ? 'inline-block rounded-md bg-yellow-200 px-2 py-1 text-sm text-yellow-700'
                                                : user.status === 'offline'
                                                    ? 'inline-block rounded-md bg-gray-100 px-2 py-1 text-sm text-gray-700'
                                                    : 'inline-block rounded-md bg-gray-100 px-2 py-1 text-sm text-gray-700'}
                                >
                                    <!-- {user.status.charAt(0).toUpperCase() + user.status.slice(1)} -->
                                    {t(user.status)}
                                </span>
                            </TableBodyCell>
                            <TableBodyCell>{user.current_workload}</TableBodyCell>
                            <!-- <TableBodyCell><span class="break-words">{user.name}</span></TableBodyCell> -->
                            <TableBodyCell>
                                <span class="break-words">{user.first_name} {user.last_name}</span>
                                <div class="text-xs text-gray-500 truncate">{user.name}</div>
                            </TableBodyCell>
                            <TableBodyCell>
                                {user.roles ? user.roles : '-'}
                            </TableBodyCell>
                            <TableBodyCell>
                                <!-- Partner badges - updated with tooltips -->
                                {#if user.partners && user.partners.length > 0}
                                    {#if user.partners.length === 1}
                                        <!-- Show single partner badge -->
                                        <span
                                            class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"
                                        >
                                            <!-- <span class={`inline-block w-2 h-2 rounded-full ${getColorClass(user.partners[0].color)}`}></span> -->
                                            <Indicator size="sm" class={`mr-1 ${getColorClass(user.partners[0].color)} inline-block`} />
                                            {user.partners[0].code}
                                        </span>
                                    {:else}
                                        <!-- Show badge with count -->
                                        <div class="relative inline-block">
                                            <span
                                                class="text-white-700 inline-flex items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm cursor-pointer"
                                                data-popover-target="popover-partners-{user.id}"
                                            >
                                                <span class="flex items-center gap-1">
                                                    <!-- <span class="inline-block w-2 h-2 rounded-full bg-blue-500"></span> -->
                                                    <span class="relative flex -space-x-1">
                                                        {#each user.partners.slice(0, 3) as partners, i (partners.name)}
                                                            <!-- <span
                                                                class={`inline-block w-2 h-2 rounded-full ${getColorClass(partners.color)}`}
                                                                style="z-index: {10 - i};"
                                                            ></span> -->
                                                            <Indicator 
                                                                size="sm" 
                                                                class={`${getColorClass(partners.color)}`}
                                                                style="z-index: {10 - i};"
                                                            />
                                                        {/each}
                                                    </span>
                                                    {user.partners.length} {t('labels')}
                                                </span>
                                            </span>
                                            
                                            <!-- Tooltip for all partners -->
                                            <Tooltip triggeredBy="[data-popover-target='popover-partners-{user.id}']">
                                                <div class="py-1 px-2 max-w-xs">
                                                    <ul class="space-y-1">
                                                        {#each user.partners as partner}
                                                            <li class="flex items-center gap-1">
                                                                <!-- <span class="inline-block w-2 h-2 rounded-full bg-blue-500"></span> -->
                                                                <!-- <span class={`inline-block w-2 h-2 rounded-full ${getColorClass(partner.color)}`}></span> -->
                                                                <Indicator size="sm" class={`mr-1 ${getColorClass(partner.color)}`} />
                                                                {partner.code}
                                                            </li>
                                                        {/each}
                                                    </ul>
                                                </div>
                                            </Tooltip>
                                        </div>
                                    {/if}
                                {:else}
                                    -
                                {/if}
                            </TableBodyCell>
                            <TableBodyCell>
                                <!-- Department badges - updated with tooltips -->
                                {#if user.departments && user.departments.length > 0}
                                    {#if user.departments.length === 1}
                                        <!-- Show single department badge -->
                                        <span
                                            class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"
                                        >
                                            <!-- <span class={`inline-block w-2 h-2 rounded-full ${getColorClass(user.departments[0].color)}`}></span> -->
                                            <Indicator size="sm" class={`mr-1 ${getColorClass(user.departments[0].color)} inline-block`} />
                                            {formatTagName(user.departments[0].code)}
                                        </span>
                                    {:else}
                                        <!-- Show badge with count -->
                                        <div class="relative inline-block">
                                            <span
                                                class="text-white-700 inline-flex items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm cursor-pointer"
                                                data-popover-target="popover-departments-{user.id}"
                                            >
                                                <span class="flex items-center gap-1">
                                                    <!-- <span class="inline-block w-2 h-2 rounded-full bg-blue-500"></span> -->
                                                    <span class="relative flex -space-x-1">
                                                        {#each user.departments.slice(0, 3) as department, i (department.name)}
                                                            <!-- <span
                                                                class={`inline-block w-2 h-2 rounded-full ${getColorClass(department.color)}`}
                                                                style="z-index: {10 - i};"
                                                            ></span> -->
                                                            <Indicator 
                                                                size="sm" 
                                                                class={`${getColorClass(department.color)}`}
                                                                style="z-index: {10 - i};"
                                                            />
                                                        {/each}
                                                    </span>
                                                    {user.departments.length} {t('labels')}
                                                </span>
                                            </span>
                                            
                                            <!-- Tooltip for all departments -->
                                            <Tooltip triggeredBy="[data-popover-target='popover-departments-{user.id}']">
                                                <div class="py-1 px-2 max-w-xs">
                                                    <ul class="space-y-1">
                                                        {#each user.departments as department}
                                                            <li class="flex items-center gap-1">
                                                                <!-- <span class="inline-block w-2 h-2 rounded-full bg-blue-500"></span> -->
                                                                <!-- <span class={`inline-block w-2 h-2 rounded-full ${getColorClass(department.color)}`}></span> -->
                                                                <Indicator size="sm" class={`mr-1 ${getColorClass(department.color)}`} />
                                                                {formatTagName(department.code)}
                                                            </li>
                                                        {/each}
                                                    </ul>
                                                </div>
                                            </Tooltip>
                                        </div>
                                    {/if}
                                {:else}
                                    -
                                {/if}
                            </TableBodyCell>
                            <TableBodyCell>
                                <!-- Specialized Tag Badges -->
                                {#if user.user_tags && user.user_tags.length > 0}
                                    {#each userMatchedTags.filter((ut) => ut.id === user.id) as matched}
                                        {#if matched.tags && matched.tags.length > 0}
                                            {#if matched.tags.length === 1}
                                                <span class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
                                                    <Indicator size="sm" class={`mr-1 ${getColorClass(matched.tags[0].color)} inline-block`} />
                                                    {formatTagName(matched.tags[0].name)}
                                                </span>
                                            {:else}
                                                <div class="relative inline-block">
                                                    <span
                                                        class="inline-flex items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm cursor-pointer"
                                                        data-popover-target="popover-tags-{user.id}"
                                                    >
                                                        <span class="flex items-center gap-1">
                                                            <span class="relative flex -space-x-1">
                                                                {#each matched.tags.slice(0, 3) as tag, i (tag.name)}
                                                                    <Indicator 
                                                                        size="sm" 
                                                                        class={`${getColorClass(tag.color)}`}
                                                                        style="z-index: {10 - i};"
                                                                    />
                                                                {/each}
                                                            </span>
                                                            {matched.tags.length} {t('labels')}
                                                        </span>
                                                    </span>

                                                    <!-- Tooltip for all tags -->
                                                    <Tooltip triggeredBy="[data-popover-target='popover-tags-{user.id}']">
                                                        <div class="py-1 px-2 max-w-xs">
                                                            <ul class="space-y-1">
                                                                {#each matched.tags as tag}
                                                                    <li class="flex items-center gap-1">
                                                                        <Indicator size="sm" class={`mr-1 ${getColorClass(tag.color)}`} />
                                                                        {formatTagName(tag.name)}
                                                                    </li>
                                                                {/each}
                                                            </ul>
                                                        </div>
                                                    </Tooltip>
                                                </div>
                                            {/if}
                                        {:else}
                                            <!-- Show when user has tag IDs but no matching tag data -->
                                            <span class="text-gray-500 italic">No matching tags</span>
                                        {/if}
                                    {/each}
                                {:else}
                                -
                                {/if}

                            </TableBodyCell>
                            <!-- <TableBodyCell>{timeAgo(user.last_active)}</TableBodyCell> -->
                            <TableBodyCell>
                                <div>{displayDate(user.last_active).date}</div>
                                <div>{displayDate(user.last_active).time}</div>
                            </TableBodyCell>
                        </TableBodyRow>
                    {/each}
                {/if}
            </TableBody>
        </Table>

        <!-- Pagination Layout -->
        <Pagination {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />
	</div>
</div>

<style>
	.break-words {
		word-break: break-word;
		white-space: normal;
	}
</style>