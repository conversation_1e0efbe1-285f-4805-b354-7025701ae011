<script lang="ts">
	import 'tailwindcss/tailwind.css';
	import Nav from '$lib/components/nav.svelte';
	import Sidebar from '$lib/components/sidebar.svelte';
	import Footer from '$lib/components/footer.svelte';
	import type { LayoutData } from './$types';
	import { initializeLanguagePreference } from '$lib/stores/i18n';
	import type { LanguageCode } from '$lib/stores/user';

	export let data: LayoutData;

	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';

	$: ({ role, status } = data);

	// Initialize language preference immediately when data is available
	$: if (data.preferred_language) {
		initializeLanguagePreference(data.preferred_language as LanguageCode);
	} else {
		// Initialize without user preference (will check cookies/localStorage)
		initializeLanguagePreference();
	}

	onMount(() => {
		if (!data.isLogin) {
			goto('/login'); // Redirect to login page if not logged in
		}
	});
</script>

<!-- <Nav
	isLogin={data.isLogin}
	id={data.id}
	name_avartar={data.name_avartar}
	fullname={data.fullname}
	email={data.email}
	status={status}
    role={role}
/> -->

<div class="flex h-screen bg-gray-100">
	<!-- Sidebar component handles its own minimizing -->
	<Sidebar
		isLogin={data.isLogin}
		id={data.id}
		name_avartar={data.name_avartar}
		fullname={data.fullname}
		email={data.email}
		{status}
		{role}
	/>

	<!-- Main Content -->
	<div class="flex-1 overflow-auto">
		<!-- <div class="p-4"> -->
		<div>
			<!-- <div class="container mx-auto mb-10"> -->
			<div class="h-full w-full">
				<slot />
			</div>
		</div>
	</div>
</div>

<Footer />
