import type { PageServerLoad, Actions } from "./$types";
import { error, redirect } from "@sveltejs/kit";
import { superValidate } from "sveltekit-superforms";
import { formSchema } from "./schema";
import { zod } from "sveltekit-superforms/adapters";
// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import { user, type User } from "../../../lib/stores/user";

export const load: PageServerLoad = async () => {
	const form = await superValidate(zod(formSchema))

	return {
		form: form,
	};
};

export const actions: Actions = {
	login: async ({ cookies, request }) => {
		// You can console.log(event) to see the object inside.
		// console.log(event);
		const form = await superValidate(request, zod(formSchema));
		if (!form.valid) {
			form.message = error(400, "Invalid Input")
		}
		else {
			const res = await login(form.data);
			form.message = res;
			form.message.status = "ok"
			if (res.message == "Login Successfully") {
				cookies.set("isLogin", 'true', { path: '/' })
				cookies.set("username", res.data.username, { path: '/' })
				cookies.set("access_token", res.data.access_token, { path: '/' })
				cookies.set("refresh_token", res.data.refresh_token, { path: '/' })
				cookies.set("user_role", res.data.role.name, { path: '/' })
				
				let userinfo:User = await getUser(res.data.username, res.data.access_token);
				userinfo.access_token = res.data.access_token;
				
				// Set the user's preferred language in a cookie so it's available immediately
				// if (userinfo.preferred_language) {
				// 	cookies.set("preferred_language", userinfo.preferred_language, { path: '/' });
				// }


				
				user.set(userinfo)
				redirect(300, '/')
			}
			else {
				form.message.status = "fail"
			}
		}
		// console.log(form)
		return { form };
	}
};

async function login(data: object) {
	const url = new URL('/user/login/', getBackendUrl());
	const myHeaders = new Headers();
	myHeaders.append("Content-Type", "application/json");
	const raw = JSON.stringify(data);
	const requestOptions: RequestInit = {
		method: "POST",
		headers: myHeaders,
		body: raw,
		redirect: "follow"
	};
	const tmp = await fetch(`${url}`, requestOptions);
	// console.log(tmp);
	const res = await (tmp).json();
	return res;
}

async function getUser(username:string, access_token:string) {
	// const url = new URL(`/user/api/user/?name=${username}`, PUBLIC_BACKEND_URL);
	const url = new URL(`/user/api/users/me/infos/`, getBackendUrl());
	const myHeaders = new Headers();
	myHeaders.append("Content-Type", "application/json");
	myHeaders.append("Authorization", `Bearer ${access_token}`);

	const requestOptions: RequestInit = {
		method: "GET",
		headers: myHeaders,
		redirect: "follow"
	};
	const res = await (await fetch(`${url}`, requestOptions)).json();

    // TODO - Delete this
    // console.log(`src\routes\(login)\login\+page.server.ts's res - ${JSON.stringify(res)}`)

	//BUG: There is the bug in API. We always get full list of users.ıı
	// Since we only login with `system`, I can simply pick id=2

	// // id,	created_by,	updated_by,	password,	last_login,	is_superuser,	
	// // username,	first_name,	last_name,	email,	is_staff,	is_active,	date_joined,	employee_id,	name,	created_on,	updated_on,	groups, user_permissions,
	// let {created_by,	updated_by,	password,	last_login,	is_superuser, 
	// 		is_staff,	is_active,	date_joined,	employee_id,
	// 		created_on,	updated_on,	groups, user_permissions,
	// 	...userInfo} = res[1]


    let {created_by,	updated_by,	password,	last_login,	is_superuser, 
                is_staff,	is_active,	date_joined,	employee_id,
                created_on,	updated_on,	groups, user_permissions,
            ...userInfo} = res

	return userInfo;
}
