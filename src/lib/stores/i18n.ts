// src/stores/i18n.ts
import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';
import en from '$lib/locales/en.json';
import th from '$lib/locales/th.json';

const dictionaries = { en, th } as const;

/* ------------ language store ------------ */
const getInitialLanguage = (): 'en' | 'th' => {
	if (browser) {
		// First try localStorage (immediate)
		const storedLang = localStorage.getItem('lang');
		if (storedLang === 'en' || storedLang === 'th') {
			return storedLang;
		}
		
		// Default to English initially
		return 'en';
	}
	return 'en';
};

// Create the store with initial value from localStorage
const stored = browser && getInitialLanguage();
export const language = writable<'en' | 'th'>(stored);

// Add a delayed cookie check that updates the store if needed
if (browser) {
	// Wait for cookies to be available (after page load)
	setTimeout(() => {
		const cookieLang = document.cookie
			.split('; ')
			.find(row => row.startsWith('preferred_language='))
			?.split('=')[1];
			
		if (cookieLang === 'en' || cookieLang === 'th') {
			// Update localStorage for future visits
			localStorage.setItem('lang', cookieLang);
			// Update the store with the cookie value
			language.set(cookieLang);
		}
	}, 1000); // 500ms delay should be enough for cookies to be available
	
	// Keep localStorage in sync with the store
	language.subscribe((v) => {
		localStorage.setItem('lang', v);
	});
}

// Set user's profile language preference
export function setUserProfileLanguage(lang: 'en' | 'th') {
	if (browser && (lang === 'en' || lang === 'th')) {
		localStorage.setItem('lang', lang);
		language.set(lang);
	}
}

/* ------------ active dictionary ------------ */
export const dict = derived(language, ($l) => dictionaries[$l] ?? dictionaries.en);

/* ------------ helper ------------ */
export function t(key: string): string {
	const d = get(dict);
	return (d && (d as Record<string, string>)[key]) ?? key;
}
