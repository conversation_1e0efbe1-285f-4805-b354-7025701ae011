// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';
import type { Customer, CustomerPlatformIdentity, Message } from '$lib/types/customer';

import type { CustomerResponse, CustomerTicketResponse, CustomerPoliciesResponse, CustomerNoteResponse } from "../../types/customer";
import { ApiError } from "../../client/errors";

export class CustomerService {
    private baseUrl = `${getBackendUrl()}/customer`;
    // private baseUrl = `${PUBLIC_BACKEND_URL.replace(/\/$/, '')}`;


    async getAll(token: string): Promise<CustomerResponse> {
        // TODO - Delete this
        // console.log(`CustomerService's getAll's baseUrl - ${this.baseUrl}`)

        try {
            const response = await fetch(`${this.baseUrl}/api/customers/paginated/`, {
                // const response = await fetch(`${this.baseUrl}/customer/api/customers/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customers: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customers:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customers'
            };
        }
    }

    async getById(id: string, token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/details/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customers: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customer'
            };
        }
    }

    async putById(id: string, customerData, token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/details/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(customerData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            console.info(`putById method - ${response_json.message}`);
            return {
                customers: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error updating customer:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to updating customer'
            };
        }
    }

    async deleteById(id: string, token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            console.info(`deleteById method - ${response_json.message}`);
            return {
                customers: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error deleting customer:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to deleting customer'
            };
        }
    }

    async getCustomerTickets(id: string, token: string): Promise<CustomerTicketResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/tickets/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customer_tickets: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer:', error);
            return {
                customer_tickets: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customer'
            };
        }
    }

    async getCustomerOwnPolicies(id: string, token: string): Promise<CustomerPoliciesResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/policies/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;
            // console.log(response_json)
            return {
                customer_policies: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer:', error);
            return {
                customer_policies: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : "Failed to fetch a customer's policies"
            };
        }
    }

    async getCustomerNotes(id: string, token: string): Promise<CustomerNoteResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/notes/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customer_notes: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer:', error);
            return {
                customer_notes: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : "Failed to fetch a customer's policies"
            };
        }
    }
    
    // ========================= Customer Tag Methods =========================
    // GET /user/api/customer-tag/
    async getAllTags(token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customer-tag/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customers: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer tags:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customer tags'
            };
        }
    }

    async createCustomerTag(tagData: { name: string }, token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customer-tag/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(tagData)
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }
            
            const response_json = await response.json();
            const res_status = response.status;
            
            return {
                customers: [response_json.data],
                res_status: res_status,
                res_msg: response_json.message
            };
        } catch (error) {
            console.error('Error creating customer tag:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to create customer tag'
            };
        }
    }

    async deleteCustomerTag(tagId: string, token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customer-tag/${tagId}/`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }
            
            const response_json = await response.json();
            const res_status = response.status;
            
            return {
                customers: [],
                res_status: res_status,
                res_msg: response_json.message
            };
        } catch (error) {
            console.error('Error deleting customer tag:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to delete customer tag'
            };
        }
    }

    async updateCustomerTag(tagId: string, tagData: { name: string }, token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customer-tag/${tagId}/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(tagData)
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }
            
            const response_json = await response.json();
            const res_status = response.status;
            
            return {
                customers: [response_json.data],
                res_status: res_status,
                res_msg: response_json.message
            };
        } catch (error) {
            console.error('Error updating customer tag:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to update customer tag'
            };
        }
    }

    async assignTagsById(id: string, tagData: { tag_ids: (string | number)[] }, token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/tags/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(tagData)
            });
    
            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }
    
            const response_json = await response.json();
            const res_status = response.status;
    
            console.info(`assignTagsById method - ${response_json.message}`);
            return {
                customers: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };
    
        } catch (error) {
            console.error('Error assigning tags to user:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to assign tags to user'
            };
        }
    }

    // Fetch memories for a customer
    async getMemoryById(id: string, token: string): Promise<{
        memories: Array<{ id: number; detail_en: string; detail_th: string; created_on: string }>;
        res_status: number;
        error_msg?: string;
    }> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/memories/`,{
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(JSON.stringify(errorData), response.status);
            }

            const data = await response.json();
            return {
                memories: data,
                res_status: response.status
            };
        } catch (error: any) {
            console.error('Error fetching customer memories:', error);
            return {
                memories: [],
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch memories'
            };
        }
    }

    // Delete a memory for a customer
    async deleteMemoryById(memoryId: string, token: string): Promise<{ res_status: number; res_msg?: string; error_msg?: string }> {
        try {
            const response = await fetch(
                `${this.baseUrl}/api/customers/memories/${memoryId}/`,
                {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(JSON.stringify(errorData), response.status);
            }

            const responseJson = await response.json();
            return {
                res_status: response.status,
                res_msg: responseJson.message
            };
        } catch (error: any) {
            console.error('Error deleting customer memory:', error);
            return {
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to delete memory'
            };
        }
    }

    // Delete a memory for a customer
    async chatCenterCustomerList(token: string): Promise<{ res_status: number; res_msg?: string; error_msg?: string }> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/`,
                {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseJson = await response.json();
            return {
                res_status: response.status,
                res_msg: responseJson.message
            };
        } catch (error: any) {
            console.error('Error deleting customer memory:', error);
            return {
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customer list'
            };
        }
    }

    async getPlatformInfo(customerId: number, platformId: number, token: string): Promise<CustomerPlatformIdentity> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${customerId}/platform-identities/${platformId}/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }

            return await response.json();
        } catch (error) {
            console.error('Error fetching platform info:', error);
            throw error;
        }
    }

    async getCustomerTicketAnalyses(id: string, token: string): Promise<{
        ticket_analyses: any[];
        res_status: number;
        error_msg?: string;
    }> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/ticket-analyses/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }

            const response_json = await response.json();

            return {
                ticket_analyses: response_json,
                res_status: response.status
            };
        } catch (error) {
            console.error('Error fetching customer ticket analyses:', error);
            return {
                ticket_analyses: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customer ticket analyses'
            };
        }
    }

    async getCustomersWithFiltersAndOrdering(
        token: string, 
        filters: {
            search?: string;
            tags?: string;
            page?: number;
            limit?: number;
        } = {},
        ordering: string = 'customer_id'
    ): Promise<CustomerResponse> {
        try {
            // Build query parameters
            const params = new URLSearchParams();
            
            // Add ordering (always send ordering parameter to ensure consistency)
            if (ordering) {
                params.append('ordering', ordering);
            }   

            // Add filters
            if (filters.search && filters.search.trim()) {
                params.append('search', filters.search);
            }
            if (filters.tags && filters.tags.trim()) {
                params.append('tags', filters.tags);
            }
            if (filters.page && filters.page > 1) {
                params.append('page', filters.page.toString());
            }
            if (filters.limit) {
                params.append('limit', filters.limit.toString());
            }
            // For future implementation
            // if (filters.platform) {
            //     params.append('platform', filters.platform);
            // }
            // if (filters.tags) {
            //     params.append('tags', filters.tags);
            // }

            const queryString = params.toString();
            const url = `${this.baseUrl}/api/customers/paginated/${queryString ? '?' + queryString : ''}`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    errorData.message || 'Failed to fetch customers',
                    response.status
                );
            }

            const response_json = await response.json();

            return {
                customers: response_json,
                res_status: response.status
            };
        } catch (error) {
            console.error('Error fetching customers:', error);
            return {
                customers: [],
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customers'
            };
        }
    }



    async getFilterTags(token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/filters/tags/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    errorData.message || 'Failed to fetch tags',
                    response.status
                );
            }

            const response_json = await response.json();

            return {
                data: response_json.tags || [],
                res_status: response.status
            };
        } catch (error) {
            console.error('Error fetching filter tags:', error);
            return {
                data: [],
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch tags'
            };
        }
    }
}