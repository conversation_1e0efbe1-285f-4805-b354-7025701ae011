<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import ConversationHeader from './ConversationHeader.svelte';
	import MessageList from './MessageList.svelte';
	import MessageInput from './MessageInput.svelte';
	import { conversationStore } from '$lib/stores/conversationStore';
	import { conversationService } from '$lib/services/conversationService';
	import { platformWebSocket } from '$lib/websocket/platformWebSocket';
	import type { Message } from '$lib/types/customer';
	import { getBackendUrl } from '$src/lib/config';
	
	export let customerId: number;
	export let platformId: number;
	export let users: any[];
	export let priorities: any[];
	export let statuses: any[];
	export let topics: any[];
	export let access_token: string;

	let messages: Message[] = [];
	let loading = true;
	let connected = false;
	let customerName = '';
	let channelName = '';

	let loadingMore = false;
	let hasMore = false;
	let error: string | null = null;

	// Subscribe to conversation store
	$: platformData = $conversationStore;
	$: messages = platformData.messages.get(platformId) || [];
	$: hasMore = platformData.hasMore.get(platformId) || false;
	$: loadingMore = platformData.loadingStates.get(platformId) || false;
	
	// $: platformMessages = $conversationStore.messages.get(platformId) || [];
	// $: messages = platformMessages;
	
	// React to platformId or customerId changes
	$: if (platformId && customerId) {
		loadConversationForPlatform(customerId, platformId);
	}
	
	// onDestroy(() => {
	// 	disconnectWebSocket();
	// });

	// // Set up event listeners
	// onMount(() => {
	// 	if (typeof window !== 'undefined') {
	// 		window.addEventListener('platform-new-message', handleNewMessage);
	// 	}
	// });
	
	// onDestroy(() => {
	// 	if (typeof window !== 'undefined') {
	// 		window.removeEventListener('platform-new-message', handleNewMessage);
	// 	}
	// 	disconnectWebSocket();
	// 	// Clear conversation when component is destroyed
	// 	if (platformId) {
	// 		conversationStore.clearConversation(platformId);
	// 	}
	// });
	
	// async function loadConversationForPlatform(custId: number, platId: number) {
	// 	// Disconnect from previous WebSocket if any
	// 	disconnectWebSocket();
		
	// 	// Clear previous messages to show loading state
	// 	messages = [];
		
	// 	try {
	// 		loading = true;
			
	// 		// Load platform info
	// 		const platformResponse = await fetch(
	// 			`${getBackendUrl()}/customer/api/customers/${custId}/platform-identities/${platId}/`,
	// 			{
	// 				credentials: 'include'
	// 			}
	// 		);
			
	// 		if (platformResponse.ok) {
	// 			const platformData = await platformResponse.json();
				
	// 			// Handle both single result and array of results
	// 			const platform = Array.isArray(platformData.results) 
	// 				? platformData.results[0] 
	// 				: platformData;
				
	// 			customerName = platform.display_name || platform.platform_username || 'Unknown User';
	// 			channelName = platform.channel_name || platform.platform;
	// 		}
			
	// 		// Load messages using the store's built-in method
	// 		await conversationStore.loadConversation(custId, platId);
			
	// 		// Get the loaded messages to check for unread ones
	// 		const loadedMessages = platformMessages;
			
	// 		// Mark messages as read
	// 		const unreadMessageIds = loadedMessages
	// 			.filter((msg: Message) => !msg.is_self && msg.status !== 'READ')
	// 			.map((msg: Message) => msg.id);
			
	// 		if (unreadMessageIds.length > 0) {
	// 			markMessagesAsRead(unreadMessageIds);
	// 		}
			
	// 		// Connect WebSocket for this platform
	// 		connectWebSocket(custId, platId);
			
	// 	} catch (error) {
	// 		console.error('Error loading conversation:', error);
	// 	} finally {
	// 		loading = false;
	// 	}
	// }

	async function loadConversationForPlatform(custId: number, platId: number) {
		// Reset error state
		error = null;
		
		// Disconnect from previous WebSocket if any
		disconnectWebSocket();
		
		// Clear previous conversation
		conversationStore.clearConversation(platId);
		
		try {
			loading = true;
			
			// Load platform info
			const platformResponse = await fetch(
				`${getBackendUrl()}/customer/api/customers/${custId}/platform-identities/${platId}/`,
				{
					credentials: 'include'
				}
			);
			
			if (platformResponse.ok) {
				const platformData = await platformResponse.json();
				
				// Handle both single result and array of results
				const platform = Array.isArray(platformData.results) 
					? platformData.results[0] 
					: platformData;
				
				if (platform) {
					customerName = platform.display_name || platform.platform_username || 'Unknown User';
					channelName = platform.channel_name || platform.platform;
				}
			} else {
				console.error('Failed to load platform info');
			}
			
			// Load messages using the store's built-in method
			await conversationStore.loadConversation(custId, platId);
			
			// Get the loaded messages to check for unread ones
			const loadedMessages = messages;
			
			// Mark messages as read
			const unreadMessageIds = loadedMessages
				.filter((msg: Message) => !msg.is_self && msg.status !== 'READ')
				.map((msg: Message) => msg.id);
			
			if (unreadMessageIds.length > 0) {
				markMessagesAsRead(unreadMessageIds);
			}
			
			// Connect WebSocket for this platform
			connectWebSocket(custId, platId);
			
		} catch (err) {
			console.error('Error loading conversation:', err);
			error = 'Failed to load conversation. Please try again.';
		} finally {
			loading = false;
		}
	}
	
	function connectWebSocket(custId: number, platId: number) {
		// For the global platform WebSocket approach
		if (typeof window !== 'undefined') {
			// Subscribe to this specific platform for updates
			platformWebSocket.subscribeToPlatform(platId);
			connected = true;
		}
	}
	
	function disconnectWebSocket() {
		// Unsubscribe from the current platform if using global WebSocket
		if (platformId && typeof window !== 'undefined') {
			platformWebSocket.unsubscribeFromPlatform(platformId);
		}
		connected = false;
	}
	
	// async function markMessagesAsRead(messageIds: number[]) {
	// 	try {
	// 		await fetch(
	// 			`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/read/`,
	// 			{
	// 				method: 'POST',
	// 				credentials: 'include',
	// 				headers: {
	// 					'Content-Type': 'application/json',
	// 				},
	// 				body: JSON.stringify({
	// 					message_ids: messageIds
	// 				})
	// 			}
	// 		);
	// 	} catch (error) {
	// 		console.error('Error marking messages as read:', error);
	// 	}
	// }

	async function markMessagesAsRead(messageIds: number[]) {
		try {
			await fetch(
				`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/read/`,
				{
					method: 'POST',
					credentials: 'include',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						message_ids: messageIds
					})
				}
			);
			
			// Update message status in store
			messageIds.forEach(id => {
				conversationStore.updateMessageStatus(platformId, id, 'READ');
			});
		} catch (error) {
			console.error('Error marking messages as read:', error);
		}
	}
	
	// async function handleSendMessage(event: CustomEvent<{ content: string; type: string }>) {
	// 	const { content, type } = event.detail;
		
	// 	try {
	// 		const response = await fetch(
	// 			`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/messages/`,
	// 			{
	// 				credentials: 'include',
	// 				method: 'POST',
	// 				headers: {
	// 					'Content-Type': 'application/json',
	// 				},
	// 				body: JSON.stringify({
	// 					message: content,
	// 					message_type: type,
	// 				}),
	// 			}
	// 		);
			
	// 		if (response.ok) {
	// 			const newMessage = await response.json();
	// 			conversationStore.addMessage(platformId, newMessage);
	// 		}
	// 	} catch (error) {
	// 		console.error('Error sending message:', error);
	// 	}
	// }

	async function handleSendMessage(event: CustomEvent<{ content: string; type: string; files?: File[] }>) {
		const { content, type, files } = event.detail;
		
		try {
			// Show loading state if needed
			const hasFiles = files && files.length > 0;
			
			// Use conversation service for cleaner code
			const response = await conversationService.sendMessage(
				customerId,
				platformId,
				content,
				type,
				files
			);
			
			if (response) {
				// Add message to store
				conversationStore.addMessage(platformId, response);
				
				// Scroll to bottom after sending message
				// This would be handled in MessageList component
			}
		} catch (error) {
			console.error('Error sending message:', error);
			// Show error notification
			// You could add a toast notification here
		}
	}
	
	// async function handleLoadMore() {
	// 	if (loading || !messages.length) return;
		
	// 	const oldestMessage = messages[0];
	// 	if (oldestMessage) {
	// 		try {
	// 			loading = true;
	// 			const response = await fetch(
	// 				`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/messages/?before=${oldestMessage.id}&limit=50`,
	// 				{
	// 					credentials: 'include'
	// 				}
	// 			);
				
	// 			if (response.ok) {
	// 				const data = await response.json();
	// 				if (data.messages && data.messages.length > 0) {
	// 					// Prepend older messages
	// 					conversationStore.prependMessages(platformId, data.messages);
	// 				}
	// 			}
	// 		} catch (error) {
	// 			console.error('Error loading more messages:', error);
	// 		} finally {
	// 			loading = false;
	// 		}
	// 	}
	// }

	// async function handleLoadMore() {
	// 	if (loadingMore || !messages.length || !hasMore) return;
		
	// 	const oldestMessage = messages[0];
	// 	if (oldestMessage) {
	// 		try {
	// 			// Set loading state
	// 			conversationStore.update(state => {
	// 				state.loadingStates.set(platformId, true);
	// 				return { ...state, loadingStates: new Map(state.loadingStates) };
	// 			});
				
	// 			const response = await fetch(
	// 				`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/messages/?before=${oldestMessage.id}&limit=50`,
	// 				{
	// 					credentials: 'include'
	// 				}
	// 			);
				
	// 			if (response.ok) {
	// 				const data = await response.json();
	// 				if (data.messages && data.messages.length > 0) {
	// 					// Fix: Pass hasMore parameter correctly
	// 					conversationStore.prependMessages(
	// 						platformId, 
	// 						data.messages, 
	// 						data.has_more || false
	// 					);
	// 				}
	// 			}
	// 		} catch (error) {
	// 			console.error('Error loading more messages:', error);
	// 		} finally {
	// 			// Clear loading state
	// 			conversationStore.update(state => {
	// 				state.loadingStates.set(platformId, false);
	// 				return { ...state, loadingStates: new Map(state.loadingStates) };
	// 			});
	// 		}
	// 	}
	// }

	async function handleLoadMore() {
		if (loadingMore || !messages.length || !hasMore) return;
		
		const oldestMessage = messages[0];
		if (oldestMessage) {
			try {
				// Use the existing loadMoreMessages method which handles loading state internally
				await conversationStore.loadMoreMessages(customerId, platformId, oldestMessage.id);
			} catch (error) {
				console.error('Error loading more messages:', error);
			}
		}
	}
	
	// // Handle real-time message updates
	// function handleNewMessage(event: CustomEvent) {
	// 	const { platformId: msgPlatformId, message } = event.detail;
	// 	if (msgPlatformId === platformId) {
	// 		// The message is already added to the store by the WebSocket handler

	// 		// Force update if needed
	// 		conversationStore.addMessage(platformId, message);	
			
	// 		// We just need to mark it as read if appropriate
	// 		if (document.hasFocus() && !message.is_self && message.status !== 'READ') {
	// 			markMessagesAsRead([message.id]);
	// 		}
	// 	}
	// }

	// Handle real-time message updates
	function handleNewMessage(event: CustomEvent) {
		const { platformId: msgPlatformId, message } = event.detail;
		if (msgPlatformId === platformId) {
			// Don't add message here - PlatformIdentityList already handles it
			// Just mark as read if appropriate
			if (document.hasFocus() && !message.is_self && message.status !== 'READ') {
				markMessagesAsRead([message.id]);
			}
		}
	}

	// function handleNewMessage(event: CustomEvent) {
	// 	const { platformId, message, unreadCount } = event.detail;
		
	// 	// Update UI elements
	// 	latestMessages.set(platformId, message);
	// 	unreadCounts.set(platformId, unreadCount);
		
	// 	// Only add to store if it's not the currently selected platform
	// 	// (ConversationView will handle the selected one)
	// 	if (platformId !== selectedPlatformId) {
	// 		conversationStore.addMessage(platformId, message);
	// 	}
	// }
</script>

<div class="h-full flex flex-col">
	<ConversationHeader 
		{customerId}
		{customerName}
		{channelName}
		{connected}
		{platformId}
		{users} 
		{priorities} 
		{statuses} 
		{topics}
		{access_token}
	/>
	
	<!-- <MessageList 
		{messages}
		{loading}
		on:loadMore={handleLoadMore}
	/> -->

	<MessageList 
		{messages}
		{loading}
		{hasMore}
		on:loadMore={handleLoadMore}
	/>
	
	<MessageInput 
		on:send={handleSendMessage}
		disabled={loading}
	/>
</div>