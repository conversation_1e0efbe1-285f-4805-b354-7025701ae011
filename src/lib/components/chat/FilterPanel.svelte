<script lang="ts">
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';
	import { Button, Input, Checkbox, Datepicker } from 'flowbite-svelte';
	import { CalendarMonthOutline, CloseOutline, SearchOutline } from 'flowbite-svelte-icons';
	import { t } from '$lib/stores/i18n';

	export let isOpen = false;
	export let sortedIdentities = [];
	export let unreadCounts = new Map();
	export let filterData = {
		dateRange: null,
		customStartDate: null,
		customEndDate: null,
		platforms: new Set(['All']),
		channels: new Set(['All']),
		unreadFilter: new Set(['All']),
		searchText: ''
	};

	const dispatch = createEventDispatcher();

	// Filter options
	const dateRangeOptions = [
		{ id: 'today', label: 'Today' },
		{ id: 'week', label: 'This Week' },
		{ id: 'month', label: 'This Month' },
		{ id: 'custom', label: 'Custom Range' }
	];

	const unreadFilterOptions = [
		{ id: 'All', label: 'All Messages' },
		{ id: 'With unread', label: 'With Unread Messages' },
		{ id: 'No unread', label: 'No Unread Messages' },
		{ id: '1-5 unread', label: '1-5 Unread' },
		{ id: '6+ unread', label: '6+ Unread' }
	];

	// Dynamic filter options based on actual data
	$: platformOptions = [
		{ id: 'All', label: 'All Platforms', color: 'bg-gray-100 text-gray-700' },
		...Array.from(new Set(sortedIdentities.map((identity) => identity.platform))).map(
			(platform) => ({
				id: platform,
				label: platform,
				color:
					platform === 'LINE'
						? 'bg-green-100 text-green-700'
						: platform === 'WHATSAPP'
							? 'bg-green-100 text-green-700'
							: platform === 'FACEBOOK'
								? 'bg-blue-100 text-blue-700'
								: platform === 'INSTAGRAM'
									? 'bg-purple-100 text-purple-700'
									: platform === 'TELEGRAM'
										? 'bg-sky-100 text-sky-700'
										: 'bg-gray-100 text-gray-700'
			})
		)
	];

	$: channelOptions = [
		{ id: 'All', label: 'All Channels' },
		...Array.from(
			new Set(sortedIdentities.map((identity) => identity.channel_name || 'No Channel'))
		).map((channel) => ({ id: channel, label: channel }))
	];

	let filterPanelElement: HTMLElement;

	// Local state for form inputs
	let selectedDateRange = filterData.dateRange;
	let customStartDate = filterData.customStartDate;
	let customEndDate = filterData.customEndDate;
	let selectedPlatforms = new Set(filterData.platforms);
	let selectedChannels = new Set(filterData.channels);
	let selectedUnreadFilter = new Set(filterData.unreadFilter);
	let searchText = filterData.searchText;

	$: showCustomDateInputs = selectedDateRange === 'custom';

	// Handle click outside to close
	const handleClickOutside = (event: MouseEvent) => {
		console.log('Click outside detected, isOpen:', isOpen);
		if (isOpen && filterPanelElement && !filterPanelElement.contains(event.target as Node)) {
			// Also check if the click is on the filter button itself (to prevent immediate closing)
			const target = event.target as Element;
			const filterButton = target.closest('[aria-label="Filter conversations"]');
			console.log('Filter button found:', !!filterButton);
			if (!filterButton) {
				console.log('Closing panel due to click outside');
				closePanel();
			}
		}
	};

	// Handle escape key to close
	const handleKeydown = (event: KeyboardEvent) => {
		if (event.key === 'Escape' && isOpen) {
			closePanel();
		}
	};

	// Track if event listeners are attached
	let listenersAttached = false;

	// Reactive statement to manage event listeners based on isOpen state
	$: if (typeof document !== 'undefined') {
		if (isOpen && !listenersAttached) {
			// Add a small delay to prevent immediate closing from the same click that opened the panel
			setTimeout(() => {
				console.log('Adding event listeners');
				document.addEventListener('click', handleClickOutside);
				document.addEventListener('keydown', handleKeydown);
				listenersAttached = true;
			}, 50);
		} else if (!isOpen && listenersAttached) {
			console.log('Removing event listeners');
			document.removeEventListener('click', handleClickOutside);
			document.removeEventListener('keydown', handleKeydown);
			listenersAttached = false;
		}
	}

	onMount(() => {
		return () => {
			// Cleanup on component destroy
			if (typeof document !== 'undefined') {
				document.removeEventListener('click', handleClickOutside);
				document.removeEventListener('keydown', handleKeydown);
			}
		};
	});

	function closePanel() {
		isOpen = false;
		dispatch('close');
	}

	function clearAllFilters() {
		selectedDateRange = null;
		customStartDate = null;
		customEndDate = null;
		selectedPlatforms = new Set(['All']);
		selectedChannels = new Set(['All']);
		selectedUnreadFilter = new Set(['All']);
		searchText = '';
		applyFilters();
	}

	function applyFilters() {
		const updatedFilterData = {
			dateRange: selectedDateRange,
			customStartDate,
			customEndDate,
			platforms: selectedPlatforms,
			channels: selectedChannels,
			unreadFilter: selectedUnreadFilter,
			searchText
		};

		dispatch('apply', updatedFilterData);
	}

	function togglePlatform(platformId: string) {
		if (platformId === 'All') {
			selectedPlatforms = new Set(['All']);
		} else {
			selectedPlatforms.delete('All');
			if (selectedPlatforms.has(platformId)) {
				selectedPlatforms.delete(platformId);
			} else {
				selectedPlatforms.add(platformId);
			}

			// If no platforms selected, select All
			if (selectedPlatforms.size === 0) {
				selectedPlatforms.add('All');
			}
		}
		selectedPlatforms = selectedPlatforms; // Trigger reactivity
	}

	function toggleChannel(channelId: string) {
		if (channelId === 'All') {
			selectedChannels = new Set(['All']);
		} else {
			selectedChannels.delete('All');
			if (selectedChannels.has(channelId)) {
				selectedChannels.delete(channelId);
			} else {
				selectedChannels.add(channelId);
			}

			// If no channels selected, select All
			if (selectedChannels.size === 0) {
				selectedChannels.add('All');
			}
		}
		selectedChannels = selectedChannels; // Trigger reactivity
	}

	function toggleUnreadFilter(filterId: string) {
		if (filterId === 'All') {
			selectedUnreadFilter = new Set(['All']);
		} else {
			selectedUnreadFilter.delete('All');
			if (selectedUnreadFilter.has(filterId)) {
				selectedUnreadFilter.delete(filterId);
			} else {
				selectedUnreadFilter.add(filterId);
			}

			// If no filters selected, select All
			if (selectedUnreadFilter.size === 0) {
				selectedUnreadFilter.add('All');
			}
		}
		selectedUnreadFilter = selectedUnreadFilter; // Trigger reactivity
	}

	// Count active filters
	$: activeFiltersCount = [
		selectedDateRange ? 1 : 0,
		selectedPlatforms.size > 1 || !selectedPlatforms.has('All') ? 1 : 0,
		selectedChannels.size > 1 || !selectedChannels.has('All') ? 1 : 0,
		selectedUnreadFilter.size > 1 || !selectedUnreadFilter.has('All') ? 1 : 0,
		searchText ? 1 : 0
	].reduce((sum, count) => sum + count, 0);
</script>

{#if isOpen}
	<div
		bind:this={filterPanelElement}
		class="absolute left-0 top-full z-50 mt-2 w-80 rounded-lg border border-gray-200 bg-white p-4 shadow-lg"
		role="dialog"
		aria-label="Filter conversations"
		aria-modal="true"
	>
		<!-- Header -->
		<div class="mb-4 flex items-center justify-between">
			<h3 class="text-lg font-semibold text-gray-900">{t('filter')}</h3>
			<div class="flex items-center gap-2">
				{#if activeFiltersCount > 0}
					<span class="text-sm text-gray-500">
						{activeFiltersCount} active
					</span>
				{/if}
				<button
					type="button"
					on:click={closePanel}
					class="rounded-lg p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
					aria-label="Close filter panel"
				>
					<CloseOutline class="h-5 w-5" />
				</button>
			</div>
		</div>

		<!-- Search Filter -->
		<div class="mb-4">
			<label for="filter-search" class="mb-2 block text-sm font-medium text-gray-700">
				Search Conversations
			</label>
			<div class="relative">
				<SearchOutline class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
				<Input
					id="filter-search"
					bind:value={searchText}
					placeholder="Search by name, platform, message..."
					class="pl-10"
					on:input={applyFilters}
				/>
			</div>
		</div>

		<!-- Date Range Filter -->
		<div class="mb-4">
			<label class="mb-2 block text-sm font-medium text-gray-700">
				<CalendarMonthOutline class="mr-1 inline h-4 w-4" />
				Date Range
			</label>
			<div class="space-y-2">
				{#each dateRangeOptions as option}
					<label class="flex items-center">
						<input
							type="radio"
							bind:group={selectedDateRange}
							value={option.id}
							on:change={applyFilters}
							class="mr-2 text-blue-600 focus:ring-blue-500"
						/>
						<span class="text-sm text-gray-700">{option.label}</span>
					</label>
				{/each}
			</div>

			{#if showCustomDateInputs}
				<div class="mt-3 space-y-2">
					<div>
						<label class="block text-xs text-gray-600">Start Date</label>
						<Datepicker
							bind:value={customStartDate}
							placeholder="Select start date"
							on:change={applyFilters}
						/>
					</div>
					<div>
						<label class="block text-xs text-gray-600">End Date</label>
						<Datepicker
							bind:value={customEndDate}
							placeholder="Select end date"
							on:change={applyFilters}
						/>
					</div>
				</div>
			{/if}
		</div>

		<!-- Platform Filter -->
		<div class="mb-4">
			<label class="mb-2 block text-sm font-medium text-gray-700">Platform</label>
			<div class="max-h-32 space-y-1 overflow-y-auto">
				{#each platformOptions as platform}
					<label class="flex items-center rounded p-2 hover:bg-gray-50">
						<Checkbox
							checked={selectedPlatforms.has(platform.id)}
							on:change={() => {
								togglePlatform(platform.id);
								applyFilters();
							}}
							class="mr-2"
						/>
						<span class="flex-1 text-sm text-gray-700">{platform.label}</span>
						{#if platform.id !== 'All'}
							<span class="rounded-full px-2 py-0.5 text-xs {platform.color}">
								{platform.id}
							</span>
						{/if}
					</label>
				{/each}
			</div>
		</div>

		<!-- Channel Filter -->
		<div class="mb-4">
			<label class="mb-2 block text-sm font-medium text-gray-700">Channel</label>
			<div class="max-h-32 space-y-1 overflow-y-auto">
				{#each channelOptions as channel}
					<label class="flex items-center rounded p-2 hover:bg-gray-50">
						<Checkbox
							checked={selectedChannels.has(channel.id)}
							on:change={() => {
								toggleChannel(channel.id);
								applyFilters();
							}}
							class="mr-2"
						/>
						<span class="text-sm text-gray-700">{channel.label}</span>
					</label>
				{/each}
			</div>
		</div>

		<!-- Unread Messages Filter -->
		<div class="mb-4">
			<label class="mb-2 block text-sm font-medium text-gray-700">Unread Messages</label>
			<div class="max-h-32 space-y-1 overflow-y-auto">
				{#each unreadFilterOptions as filter}
					<label class="flex items-center rounded p-2 hover:bg-gray-50">
						<Checkbox
							checked={selectedUnreadFilter.has(filter.id)}
							on:change={() => {
								toggleUnreadFilter(filter.id);
								applyFilters();
							}}
							class="mr-2"
						/>
						<span class="text-sm text-gray-700">{filter.label}</span>
					</label>
				{/each}
			</div>
		</div>

		<!-- Actions -->
		<div class="flex justify-between border-t border-gray-200 pt-4">
			<Button
				color="none"
				class="text-gray-600 hover:bg-gray-100"
				on:click={clearAllFilters}
				disabled={activeFiltersCount === 0}
			>
				{t('clear')}
			</Button>
			<Button color="blue" on:click={closePanel}>Done</Button>
		</div>
	</div>
{/if}
